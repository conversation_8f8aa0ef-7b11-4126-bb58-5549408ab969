import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/property_access_service.dart';

/// Widget that shows/hides content based on property access
class PropertyBasedWidget extends ConsumerWidget {
  final String propertyId;
  final Widget child;
  final Widget? fallback;
  final List<String>? requiredPermissions;

  const PropertyBasedWidget({
    super.key,
    required this.propertyId,
    required this.child,
    this.fallback,
    this.requiredPermissions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check basic property access
    final propertyAccessAsync = ref.watch(propertyAccessProvider(propertyId));

    return propertyAccessAsync.when(
      data: (hasAccess) {
        if (!hasAccess) {
          return fallback ?? const SizedBox.shrink();
        }

        // If no specific permissions required, show the child
        if (requiredPermissions == null || requiredPermissions!.isEmpty) {
          return child;
        }

        // Check specific permissions
        return _PermissionChecker(
          propertyId: propertyId,
          requiredPermissions: requiredPermissions!,
          child: child,
          fallback: fallback,
        );
      },
      loading: () => fallback ?? const SizedBox.shrink(),
      error: (_, __) => fallback ?? const SizedBox.shrink(),
    );
  }
}

class _PermissionChecker extends ConsumerWidget {
  final String propertyId;
  final List<String> requiredPermissions;
  final Widget child;
  final Widget? fallback;

  const _PermissionChecker({
    required this.propertyId,
    required this.requiredPermissions,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check all required permissions
    final permissionChecks = requiredPermissions.map((permission) {
      final request = PropertyPermissionRequest(
        propertyId: propertyId,
        permission: permission,
      );
      return ref.watch(propertyPermissionProvider(request));
    }).toList();

    // Wait for all permission checks to complete
    final allLoaded = permissionChecks.every((check) => check.hasValue);
    if (!allLoaded) {
      return fallback ?? const SizedBox.shrink();
    }

    // Check if any permission check failed
    final hasAllPermissions = permissionChecks.every((check) => 
      check.hasValue && check.value == true
    );

    if (hasAllPermissions) {
      return child;
    } else {
      return fallback ?? const SizedBox.shrink();
    }
  }
}

/// Widget that shows content only for property managers
class PropertyManagerWidget extends StatelessWidget {
  final String propertyId;
  final Widget child;
  final Widget? fallback;

  const PropertyManagerWidget({
    super.key,
    required this.propertyId,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return PropertyBasedWidget(
      propertyId: propertyId,
      requiredPermissions: const ['properties.manage'],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget that shows content only for property supervisors
class PropertySupervisorWidget extends StatelessWidget {
  final String propertyId;
  final Widget child;
  final Widget? fallback;

  const PropertySupervisorWidget({
    super.key,
    required this.propertyId,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return PropertyBasedWidget(
      propertyId: propertyId,
      requiredPermissions: const ['properties.supervise'],
      fallback: fallback,
      child: child,
    );
  }
}

/// AppBar that adapts based on property access
class PropertyBasedAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String propertyId;
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;

  const PropertyBasedAppBar({
    super.key,
    required this.propertyId,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final propertyAccessAsync = ref.watch(propertyAccessProvider(propertyId));

    return propertyAccessAsync.when(
      data: (hasAccess) {
        if (!hasAccess) {
          return AppBar(
            title: Text(title),
            leading: leading,
            automaticallyImplyLeading: automaticallyImplyLeading,
            backgroundColor: backgroundColor ?? Colors.red[100],
            actions: [
              IconButton(
                icon: const Icon(Icons.block),
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You do not have access to this property'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              ),
            ],
          );
        }

        return AppBar(
          title: Text(title),
          leading: leading,
          automaticallyImplyLeading: automaticallyImplyLeading,
          backgroundColor: backgroundColor,
          actions: actions,
        );
      },
      loading: () => AppBar(
        title: Text(title),
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor ?? Colors.grey[100],
      ),
      error: (_, __) => AppBar(
        title: Text(title),
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor ?? Colors.red[100],
        actions: [
          IconButton(
            icon: const Icon(Icons.error),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Error checking property access'),
                  backgroundColor: Colors.red,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
